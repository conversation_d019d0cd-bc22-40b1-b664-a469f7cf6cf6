import type { INodeProperties, IHttpRequestOptions } from 'n8n-workflow';

const showOnlyForLeadCreate = {
    operation: ['create'],
    resource: ['lead'],
};



export const customFieldsDescription: INodeProperties[] = [
    {
        displayName: "Custom Fields",
        name: "customFields",
        type: "fixedCollection",
        placeholder: "Add Custom Field",
        default: {},
        displayOptions: {
            show: showOnlyForLeadCreate,
        },
        description: "Add custom fields for the lead",
        typeOptions: {
            multipleValues: true,
        },
        options: [
            {
                displayName: "Property",
                name: "property",
                values: [
                    {
                        displayName: "Field Name or ID",
                        name: "name",
                        type: "options",
                        typeOptions: {
                            loadOptionsMethod: 'getLeadCustomFields',
                        },
                        default: '',
                        description: 'Choose from the list, or specify an ID using an <a href="https://docs.n8n.io/code/expressions/">expression</a>',
                    },
                    {
                        displayName: "Field Value",
                        name: "value",
                        type: "string",
                        default: '',
                        description: 'Choose from the list, or specify an ID using an <a href="https://docs.n8n.io/code/expressions/">expression</a>',
                    }
                ]
            },
        ],
        routing: {
            send: {
                preSend: [
                    async function (this: {
                        getNodeParameter: (parameterName: string, defaultValue?: unknown) => unknown;
                    }, requestOptions: IHttpRequestOptions) {
                        const customFields = this.getNodeParameter('customFields.customField', []) as Array<{
                            fieldName: string;
                            textValue?: string;
                            numberValue?: number;
                            dateValue?: string;
                            booleanValue?: boolean;
                            picklistValue?: string;
                        }>;

                        // Add each custom field to the request body
                        customFields.forEach(field => {
                            if (field.fieldName) {
                                try {
                                    // Parse the field metadata from the fieldName value
                                    const fieldMeta = JSON.parse(field.fieldName);
                                    const fieldName = fieldMeta.name;
                                    const fieldType = fieldMeta.type;

                                    let value: unknown;

                                    // Determine the value based on field type
                                    switch (fieldType) {
                                        case 'TEXT_FIELD':
                                        case 'EMAIL_FIELD':
                                        case 'URL_FIELD':
                                        case 'TEXTAREA_FIELD':
                                            value = field.textValue;
                                            break;
                                        case 'NUMBER_FIELD':
                                            value = field.numberValue;
                                            break;
                                        case 'DATE_FIELD':
                                            value = field.dateValue;
                                            break;
                                        case 'BOOLEAN_FIELD':
                                            value = field.booleanValue;
                                            break;
                                        case 'PICKLIST':
                                            value = field.picklistValue;
                                            break;
                                        default:
                                            value = field.textValue; // fallback to text
                                    }

                                    // Only add if value is not empty/undefined
                                    if (value !== undefined && value !== '' && value !== null) {
                                        if (!requestOptions.body) {
                                            requestOptions.body = {};
                                        }
                                        if (typeof requestOptions.body === 'object' && requestOptions.body !== null && !Array.isArray(requestOptions.body)) {
                                            (requestOptions.body as Record<string, unknown>)[fieldName] = value;
                                        }
                                    }
                                } catch {
                                    // If parsing fails, treat as simple field name
                                    const value = field.textValue || field.numberValue || field.dateValue || field.booleanValue || field.picklistValue;
                                    if (value !== undefined && value !== '' && value !== null) {
                                        if (!requestOptions.body) {
                                            requestOptions.body = {};
                                        }
                                        if (typeof requestOptions.body === 'object' && requestOptions.body !== null && !Array.isArray(requestOptions.body)) {
                                            (requestOptions.body as Record<string, unknown>)[field.fieldName] = value;
                                        }
                                    }
                                }
                            }
                        });

                        return requestOptions;
                    }
                ]
            },
        },
    },
];
